import { When, Then, DataTable, Given, Before } from "@badeball/cypress-cucumber-preprocessor";
import { mainPage } from "../../../pages/mainPage";
import { ButtonActionMap, ButtonActions, ValidationButtonTypes } from "../../../support/helperFunction/eventScreenHelper";

Before( { tags: '@file-screen' }, () => {
  cy.LoginLandingPage();
  cy.intercept('GET', /api\/v1\/files\/\?sortBy/).as('fetchFiles');
});

Given('The user is on File Screen', () => {
  mainPage.visit();
  mainPage.clickTab('Files');
  cy.waitFilesTabIsLoaded();
})

When('The user clicks on the {string} tab', (tabName: string) => {
  mainPage.clickTab(tabName);
  if (tabName.toLowerCase() === 'files') {
    cy.waitFilesTabIsLoaded();
  } else {
    cy.waitMainPageIsLoaded();
  }
});

Then('The {string} tab should be active', (activeTabName: string) => {
  mainPage.verifyTabIsActive(activeTabName);
});

When('The user clicks the {string} breadcrumb', (breadcrumbText: string) => {
  mainPage.clickBreadcrumb(breadcrumbText);
});

Then('The page should not navigate away', () => {
  mainPage.verifyPageNotNavigated();
});

When('The user selects the file named {string}', (fileName: string) => {
  mainPage.selectFile(fileName);
});

Then('The following file details should be visible:', (dataTable: DataTable) => {
  const rows = dataTable.hashes() as unknown as Array<{ Field: string; 'Expected Value': string }>;
  mainPage.verifyFileDetails(rows);
});

When('The user clicks the file name edit button', () => {
  cy.get('[data-testid="home-detail-file-name-edit"]').should('be.visible').click();
});

When('The user changes the name to {string}', (newName: string) => {
  cy.get('[data-testid="home-detail-name"]').clear().type(newName);
  cy.get('body').click(); // Click outside to save
});

Then('The name of the file should be updated to {string}', (expectedName: string) => {
  cy.get('[data-testid="home-detail-name"]').should('contain.text', expectedName);
});

When('The user clicks on {string}', (buttonText: string) => {
  if (buttonText === 'View File') {
    cy.get('[data-testid="home-view-file-button"]').should('be.visible').click();
  } else if (buttonText === 'Delete File') {
    cy.get('[data-testid="home-delete-file-button"]').should('be.visible').click();
  }
});

Then('The user should navigate to file details page', () => {
  cy.url().should('include', '/file/');
});

Given('The user deletes the following Files if exist: {string}', (fileName: string) => {
  mainPage.deleteFileIfExists(fileName);
});

When('The user uploads test file {string}', (fileName: string) => {
  mainPage.uploadTestFile(fileName);
});

Then('The user should see a success snackbar with message {string}', (message: string) => {
  cy.get('[data-testid="snackbar-success"]').should('contain.text', message);
});

When('The user enters correct file name {string}', (fileName: string) => {
  cy.get('[data-testid="delete-confirmation-input"]').type(fileName);
});

When('The user enters wrong file name {string}', (fileName: string) => {
  cy.get('[data-testid="delete-confirmation-input"]').type(fileName);
});

Then('The user verifies delete button is enabled and clicks it', () => {
  cy.get('[data-testid="delete-confirm-button"]').should('be.enabled').click();
});

Then('The {string} button should still be disabled and textbox highlighted in red', (buttonText: string) => {
  cy.get('[data-testid="delete-confirm-button"]').should('be.disabled');
  cy.get('[data-testid="delete-confirmation-input"]').should('have.class', 'error');
});

When('The user clicks on the {string} column header', (columnName: string) => {
  mainPage.clickColumnHeader(columnName);
});

Then('{string} is sorted by {string}', (columnName: string, sortOrder: string) => {
  mainPage.verifyColumnSorted(columnName, sortOrder);
});

When('The user enters {string} into the search bar', (keyword: string) => {
  mainPage.searchFor(keyword);
});

Then('The displayed file results should contain {string}', (keyword: string) => {
  mainPage.verifySearchResults('files', keyword);
});

Then('The user should see the {string} label', (labelText: string) => {
  mainPage.verifyResultsPerPageLabel(labelText);
});

When('The user changes the results per page and verifies the following options:', (dataTable: DataTable) => {
  const rows = dataTable.hashes() as unknown as Array<{ PerPage: string }>;
  mainPage.verifyResultsPerPageOptions(rows);
});

Then('The user should see the initial pagination state', () => {
  mainPage.verifyInitialPaginationState();
});

When('The user navigates to the {string} page', (direction: string) => {
  mainPage.navigateToPage(direction);
});

Then('The pagination should update for the next page', () => {
  mainPage.verifyPaginationUpdated('next');
});

Then('The pagination should return to the initial state', () => {
  mainPage.verifyInitialPaginationState();
});

Given('The user deletes the following Files if exist:', (dataTable: DataTable) => {
  const rows = dataTable.hashes() as unknown as Array<{ fileName: string }>;
  rows.forEach(row => {
    mainPage.deleteFileIfExists(row.fileName);
  });
});
